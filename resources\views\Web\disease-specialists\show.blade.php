@extends('Layouts.app')

@section('title', 'SamRx | Disease Specialist Details')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Disease Specialist Details<span class="page-desc text-muted fs-7 fw-semibold pt-1">View comprehensive disease specialist information</span></h1>
                            </div>
                            <div class="d-flex gap-2">
                                <a href="{{ route('disease-specialists.edit', $diseaseSpecialist->id) }}" class="btn btn-primary"><i class="fas fa-edit"></i> Edit Disease Specialist</a>
                                <a href="{{ route('disease-specialists.index') }}" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to List</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="row g-6 p-5">
                                    <!-- Specialist Name -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="specialist_name" class="form-label fw-semibold fs-6 required">Specialist Name</label>
                                            <div class="form-control form-control-solid bg-light">{{ $diseaseSpecialist->specialist_name ?? 'N/A' }}</div>
                                        </div>
                                    </div>

                                    <!-- Specialist Description -->
                                    <div class="col-md-12">
                                        <div class="fv-row">
                                            <label for="specialist_description" class="form-label fw-semibold fs-6 required">Specialist Description</label>
                                            <div class="form-control form-control-solid bg-light ckeditor-content" style="min-height: 150px; overflow-y: auto;">{!! $diseaseSpecialist->specialist_description ?? 'N/A' !!}</div>
                                        </div>
                                    </div>

                                    <!-- Status -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                            <div class="form-control form-control-solid bg-light">
                                                @if($diseaseSpecialist->status === 1)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-danger">Inactive</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Created At -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="created_at" class="form-label fw-semibold fs-6 required">Created At</label>
                                            <div class="form-control form-control-solid bg-light">{{ $diseaseSpecialist->created_at ? $diseaseSpecialist->created_at->format('d M Y, h:i A') : 'N/A' }}</div>
                                        </div>
                                    </div>

                                    <!-- Updated At -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="updated_at" class="form-label fw-semibold fs-6 required">Last Updated</label>
                                            <div class="form-control form-control-solid bg-light">{{ $diseaseSpecialist->updated_at ? $diseaseSpecialist->updated_at->format('d M Y, h:i A') : 'N/A' }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
