

<?php $__env->startSection('title', 'SamRx | Drug Schedule Details'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Drug Schedule Details<span class="page-desc text-muted fs-7 fw-semibold pt-1">View comprehensive drug schedule information</span></h1>
                            </div>
                            <div class="d-flex gap-2">
                                <a href="<?php echo e(route('drug-schedules.edit', $drugSchedule->id)); ?>" class="btn btn-primary"><i class="fas fa-edit"></i> Edit Drug Schedule</a>
                                <a href="<?php echo e(route('drug-schedules.index')); ?>" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to List</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="row g-6 p-5">
                                    <!-- Schedule Name -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="schedule_name" class="form-label fw-semibold fs-6 required">Schedule Name</label>
                                            <div class="form-control form-control-solid bg-light"><?php echo e($drugSchedule->schedule_name ?? 'N/A'); ?></div>
                                        </div>
                                    </div>

                                    <!-- Schedule Description -->
                                    <div class="col-md-12">
                                        <div class="fv-row">
                                            <label for="schedule_description" class="form-label fw-semibold fs-6 required">Schedule Description</label>
                                            <div class="form-control form-control-solid bg-light ckeditor-content" style="min-height: 150px; overflow-y: auto;"><?php echo $drugSchedule->schedule_description ?? 'N/A'; ?></div>
                                        </div>
                                        <div class="d-flex flex-wrap gap-2 justify-content-start mt-3">
                                            <!-- Status -->
                                            <div>
                                                <?php if($drugSchedule->status === 1): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Inactive</span>
                                                <?php endif; ?>
                                            </div>
                                            <!-- Last Updated -->
                                            <div class="text-muted small"><i class="fas fa-clock me-1"></i><?php echo e($drugSchedule->updated_at ? $drugSchedule->updated_at->format('d M Y, h:i A') : 'N/A'); ?></div>
                                            <!-- Created By -->
                                            <div class="text-muted small"><i class="fas fa-user me-1"></i><?php echo e($drugSchedule->createdBy->full_name ?? 'N/A'); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\rx_info\resources\views/Web/drug-schedules/show.blade.php ENDPATH**/ ?>