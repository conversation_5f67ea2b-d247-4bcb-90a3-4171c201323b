@extends('Layouts.app')

@section('title', 'SamRx | Drug Receptor Details')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Drug Receptor Details<span class="page-desc text-muted fs-7 fw-semibold pt-1">View comprehensive drug receptor information</span></h1>
                            </div>
                            <div class="d-flex gap-2">
                                <a href="{{ route('drug-receptors.edit', $drugReceptor->id) }}" class="btn btn-primary"><i class="fas fa-edit"></i> Edit Drug Receptor</a>
                                <a href="{{ route('drug-receptors.index') }}" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to List</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="row g-6 p-5">
                                    <!-- Receptor Name -->
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="receptor_name" class="form-label fw-semibold fs-6 required">Receptor Name</label>
                                            <div class="form-control form-control-solid bg-light">{{ $drugReceptor->receptor_name ?? 'N/A' }}</div>
                                        </div>
                                    </div>

                                    <!-- Receptor Description -->
                                    <div class="col-md-12">
                                        <div class="fv-row">
                                            <label for="receptor_description" class="form-label fw-semibold fs-6 required">Receptor Description</label>
                                            <div class="form-control form-control-solid bg-light ckeditor-content" style="min-height: 150px; overflow-y: auto;">{!! $drugReceptor->receptor_description ?? 'N/A' !!}</div>
                                        </div>
                                        <div class="d-flex flex-wrap gap-2 justify-content-start mt-3">
                                            <!-- Status -->
                                            <div>
                                                @if($drugReceptor->status === 1)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-danger">Inactive</span>
                                                @endif
                                            </div>
                                            <!-- Last Updated -->
                                            <div class="text-muted small"><i class="fas fa-clock me-1"></i>{{ $drugReceptor->updated_at ? $drugReceptor->updated_at->format('d M Y, h:i A') : 'N/A' }}</div>
                                            <!-- Created By -->
                                            <div class="text-muted small"><i class="fas fa-user me-1"></i>{{ $drugReceptor->createdBy->full_name ?? 'N/A' }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
